/* Ant Design Layout Styles */
.site-layout .site-layout-background {
  background: #fff;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
}

/* Custom styles for the application */
.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card-head-title {
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }

  .ant-layout-sider-collapsed {
    width: 0 !important;
  }
}

/* Animation for smooth transitions */
.ant-layout-sider {
  transition: all 0.2s;
}

/* Custom button styles */
.ant-btn {
  border-radius: 6px;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}
