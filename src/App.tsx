import React from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>, Card, Row, Col, Typography, Space } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  VideoCameraOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import './App.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

function App() {
  const [collapsed, setCollapsed] = React.useState(false);

  const toggle = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="logo" style={{
          height: 32,
          margin: 16,
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6
        }} />
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['1']}
          items={[
            {
              key: '1',
              icon: <UserOutlined />,
              label: '用户管理',
            },
            {
              key: '2',
              icon: <VideoCameraOutlined />,
              label: '视频管理',
            },
            {
              key: '3',
              icon: <UploadOutlined />,
              label: '文件上传',
            },
          ]}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-background" style={{ padding: 0, background: '#fff' }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggle}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
        </Header>
        <Content
          className="site-layout-background"
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff',
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Title level={2}>MathCloud 数学云平台</Title>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card title="用户统计" bordered={false}>
                  <Text strong style={{ fontSize: '24px', color: '#1890ff' }}>1,234</Text>
                  <br />
                  <Text type="secondary">总用户数</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card title="在线用户" bordered={false}>
                  <Text strong style={{ fontSize: '24px', color: '#52c41a' }}>89</Text>
                  <br />
                  <Text type="secondary">当前在线</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card title="今日访问" bordered={false}>
                  <Text strong style={{ fontSize: '24px', color: '#faad14' }}>456</Text>
                  <br />
                  <Text type="secondary">访问次数</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card title="系统状态" bordered={false}>
                  <Text strong style={{ fontSize: '24px', color: '#f5222d' }}>正常</Text>
                  <br />
                  <Text type="secondary">运行状态</Text>
                </Card>
              </Col>
            </Row>

            <Card title="快速操作" style={{ marginTop: 16 }}>
              <Space wrap>
                <Button type="primary">创建用户</Button>
                <Button>导入数据</Button>
                <Button>系统设置</Button>
                <Button>查看日志</Button>
              </Space>
            </Card>
          </Space>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App
