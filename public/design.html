<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MasterGo 设计实现</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: #fff;
            padding: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }

        .nav {
            display: flex;
            gap: 30px;
        }

        .nav a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav a:hover {
            color: #1890ff;
        }

        /* Main Content */
        .main-content {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            color: #262626;
            margin-bottom: 20px;
            text-align: center;
        }

        .subtitle {
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 40px;
        }

        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 24px;
            transition: box-shadow 0.3s;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
        }

        .card-content {
            color: #666;
            line-height: 1.6;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .btn-primary {
            background: #1890ff;
            color: #fff;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }

        .btn-secondary:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* Statistics */
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }

        /* Footer */
        .footer {
            background: #fff;
            padding: 30px 0;
            text-align: center;
            color: #666;
            border-top: 1px solid #e8e8e8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .nav {
                gap: 20px;
            }

            .container {
                padding: 10px;
            }

            .main-content {
                padding: 20px;
            }

            .stats {
                flex-direction: column;
                gap: 20px;
            }

            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">MathCloud</div>
            <nav class="nav">
                <a href="#home">首页</a>
                <a href="#features">功能</a>
                <a href="#about">关于</a>
                <a href="#contact">联系</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <main class="main-content">
            <h1 class="page-title">数学云平台</h1>
            <p class="subtitle">为数学学习和教学提供智能化解决方案</p>

            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">1,234</span>
                    <div class="stat-label">注册用户</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">89</span>
                    <div class="stat-label">在线用户</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">456</span>
                    <div class="stat-label">今日访问</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.9%</span>
                    <div class="stat-label">系统稳定性</div>
                </div>
            </div>

            <div class="grid">
                <div class="card">
                    <h3 class="card-title">智能题库</h3>
                    <div class="card-content">
                        提供海量数学题目，支持智能推荐和个性化学习路径规划。
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-title">在线答疑</h3>
                    <div class="card-content">
                        24小时在线答疑服务，专业教师团队为您解答数学难题。
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-title">学习分析</h3>
                    <div class="card-content">
                        详细的学习数据分析，帮助学生了解学习进度和薄弱环节。
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-title">互动课堂</h3>
                    <div class="card-content">
                        支持在线直播教学，师生实时互动，提升学习效果。
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="#" class="btn btn-primary" style="margin-right: 15px;">立即开始</a>
                <a href="#" class="btn btn-secondary">了解更多</a>
            </div>
        </main>
    </div>

    <footer class="footer">
        <p>&copy; 2024 MathCloud 数学云平台. 保留所有权利.</p>
    </footer>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 卡片悬停效果
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
